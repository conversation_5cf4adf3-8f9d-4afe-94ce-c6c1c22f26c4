import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  MessageCircle,
  Trash2,
  User,
  X,
  XCircle,
} from "lucide-react";
import CommentSection from "./CommentSection";

const TicketDetailModal = ({ ticket, isOpen, onClose, onTicketAction }) => {
  if (!isOpen || !ticket) return null;

  // Determine what actions are available for this ticket
  const canComment = ["In Progress", "Approved", "Resolved", "Seen"].includes(
    ticket.status
  );
  const canClose =
    ticket.canClose &&
    ["In Progress", "Approved", "Seen"].includes(ticket.status);
  const canReopen = ticket.canReopen && ticket.status === "Resolved";
  const canDelete = ticket.canDelete;
  const canResubmit = ticket.canResubmit && ticket.status === "Rejected";
  const showRejectionReason =
    ticket.status === "Rejected" && ticket.rejectionReason;

  const getStatusIcon = (status) => {
    switch (status) {
      case "Pending Approval":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "In Progress":
        return <AlertCircle className="w-4 h-4 text-blue-500" />;
      case "Resolved":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "Rejected":
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Pending Approval":
        return "bg-yellow-100 text-yellow-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Resolved":
        return "bg-green-100 text-green-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "High":
        return "bg-red-500";
      case "Medium":
        return "bg-yellow-500";
      case "Low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-7xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-gray-900">
              Ticket Details - {ticket.id}
            </h2>
            <div className="flex items-center">
              {getStatusIcon(ticket.status)}
              <span
                className={`ml-2 px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(
                  ticket.status
                )}`}
              >
                {ticket.status}
              </span>
            </div>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Three Column Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Column - Ticket Details */}
          <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Ticket Details
              </h3>

              {/* Basic Information */}
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject
                  </label>
                  <p className="text-sm text-gray-900">{ticket.subject}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <p className="text-sm text-gray-900">{ticket.category}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <div className="flex items-center">
                    <div
                      className={`w-3 h-3 rounded-full mr-2 ${getPriorityColor(
                        ticket.priority
                      )}`}
                    ></div>
                    <span className="text-sm text-gray-900">
                      {ticket.priority}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Created
                  </label>
                  <div className="flex items-center text-sm text-gray-900">
                    <Calendar className="w-4 h-4 mr-2" />
                    {ticket.created}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Assigned Agent
                  </label>
                  <div className="flex items-center text-sm text-gray-900">
                    <User className="w-4 h-4 mr-2" />
                    {ticket.agent || "Unassigned"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Last Update
                  </label>
                  <p className="text-sm text-gray-900">{ticket.lastUpdate}</p>
                </div>
              </div>

              {/* Description */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <div className="bg-gray-50 rounded-lg p-3">
                  <p className="text-sm text-gray-900">
                    {ticket.description || "No description provided."}
                  </p>
                </div>
              </div>

              {/* Rejection Reason */}
              {showRejectionReason && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-red-700 mb-2">
                    Rejection Reason
                  </label>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="flex items-start">
                      <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-red-800">
                        {ticket.rejectionReason}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Approval Notes */}
              {ticket.approvalNotes && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-green-700 mb-2">
                    Approval Notes
                  </label>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="flex items-start">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <p className="text-sm text-green-800">
                        {ticket.approvalNotes}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Middle Column - Chat */}
          <div className="flex-1 border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Conversation
              </h3>
            </div>
            <div className="flex-1 overflow-hidden">
              {canComment ? (
                <CommentSection
                  ticketId={ticket.id}
                  canComment={canComment}
                  ticketStatus={ticket.status}
                />
              ) : (
                <div className="flex-1 flex items-center justify-center p-4">
                  <div className="text-center text-gray-500">
                    <AlertCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p className="text-sm">
                      Comments can only be added after the ticket has been
                      approved by an agent.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Column - Status & Actions */}
          <div className="w-1/4 overflow-y-auto">
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Status & Actions
              </h3>

              {/* Current Status */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Current Status
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  {getStatusIcon(ticket.status)}
                  <span
                    className={`ml-2 px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(
                      ticket.status
                    )}`}
                  >
                    {ticket.status}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                {canReopen && (
                  <button
                    onClick={() => onTicketAction("reopen", ticket)}
                    className="w-full flex items-center justify-center px-3 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                    title="Continue Chat"
                  >
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Continue Chat
                  </button>
                )}

                {canClose && (
                  <button
                    onClick={() => onTicketAction("close", ticket)}
                    className="w-full flex items-center justify-center px-3 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                    title="Close Ticket"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Close Ticket
                  </button>
                )}

                {canDelete && (
                  <button
                    onClick={() => onTicketAction("delete", ticket)}
                    className="w-full flex items-center justify-center px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                    title="Delete Ticket"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete Ticket
                  </button>
                )}

                {canResubmit && (
                  <button
                    onClick={() => onTicketAction("resubmit", ticket)}
                    className="w-full flex items-center justify-center px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    title="Resubmit Ticket"
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Resubmit Ticket
                  </button>
                )}
              </div>

              {/* Ticket Metadata */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className="text-sm font-medium text-gray-700 mb-3">
                  Ticket Information
                </h4>
                <div className="space-y-2 text-xs text-gray-600">
                  <div className="flex justify-between">
                    <span>Ticket ID:</span>
                    <span className="font-mono">{ticket.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Customer:</span>
                    <span>{ticket.customer || "N/A"}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Department:</span>
                    <span>{ticket.department || "General"}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TicketDetailModal;
