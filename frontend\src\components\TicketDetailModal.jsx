import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  MessageCircle,
  Trash2,
  User,
  X,
  XCircle,
} from "lucide-react";
import CommentSection from "./CommentSection";

const TicketDetailModal = ({ ticket, isOpen, onClose, onTicketAction }) => {
  if (!isOpen || !ticket) return null;

  // Determine what actions are available for this ticket
  const canComment = ["In Progress", "Approved", "Resolved", "Seen"].includes(
    ticket.status
  );
  const canClose =
    ticket.canClose &&
    ["In Progress", "Approved", "Seen"].includes(ticket.status);
  const canReopen = ticket.canReopen && ticket.status === "Resolved";
  const canDelete = ticket.canDelete;
  const canResubmit = ticket.canResubmit && ticket.status === "Rejected";
  const showRejectionReason =
    ticket.status === "Rejected" && ticket.rejectionReason;

  const getStatusIcon = (status) => {
    switch (status) {
      case "Pending Approval":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "In Progress":
        return <AlertCircle className="w-4 h-4 text-blue-500" />;
      case "Resolved":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "Rejected":
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Pending Approval":
        return "bg-yellow-100 text-yellow-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Resolved":
        return "bg-green-100 text-green-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "High":
        return "bg-red-500";
      case "Medium":
        return "bg-yellow-500";
      case "Low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-semibold text-gray-900">
              Ticket Details - {ticket.id}
            </h2>
            <div className="flex items-center">
              {getStatusIcon(ticket.status)}
              <span
                className={`ml-2 px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(
                  ticket.status
                )}`}
              >
                {ticket.status}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {canReopen && (
              <button
                onClick={() => onTicketAction("reopen", ticket)}
                className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                title="Continue Chat"
              >
                <MessageCircle className="w-4 h-4 mr-1" />
                Continue Chat
              </button>
            )}

            {canClose && (
              <button
                onClick={() => onTicketAction("close", ticket)}
                className="flex items-center px-3 py-1.5 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                title="Close Ticket"
              >
                <X className="w-4 h-4 mr-1" />
                Close
              </button>
            )}

            {canDelete && (
              <button
                onClick={() => onTicketAction("delete", ticket)}
                className="flex items-center px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                title="Delete Ticket"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Delete
              </button>
            )}

            {canResubmit && (
              <button
                onClick={() => onTicketAction("resubmit", ticket)}
                className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                title="Resubmit Ticket"
              >
                <Edit className="w-4 h-4 mr-1" />
                Resubmit
              </button>
            )}

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-6">
            {/* Ticket Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Subject
                  </label>
                  <p className="text-sm text-gray-900">{ticket.subject}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category
                  </label>
                  <p className="text-sm text-gray-900">{ticket.category}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <div className="flex items-center">
                    <div
                      className={`w-3 h-3 rounded-full mr-2 ${getPriorityColor(
                        ticket.priority
                      )}`}
                    ></div>
                    <span className="text-sm text-gray-900">
                      {ticket.priority}
                    </span>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Created
                  </label>
                  <div className="flex items-center text-sm text-gray-900">
                    <Calendar className="w-4 h-4 mr-2" />
                    {ticket.created}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Assigned Agent
                  </label>
                  <div className="flex items-center text-sm text-gray-900">
                    <User className="w-4 h-4 mr-2" />
                    {ticket.agent || "Unassigned"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Last Update
                  </label>
                  <p className="text-sm text-gray-900">{ticket.lastUpdate}</p>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-900">
                  {ticket.description || "No description provided."}
                </p>
              </div>
            </div>

            {/* Rejection Reason */}
            {showRejectionReason && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-red-700 mb-2">
                  Rejection Reason
                </label>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-sm text-red-800">
                      {ticket.rejectionReason}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Approval Notes */}
            {ticket.approvalNotes && (
              <div className="mb-6">
                <label className="block text-sm font-medium text-green-700 mb-2">
                  Approval Notes
                </label>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-sm text-green-800">
                      {ticket.approvalNotes}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Comments Section */}
            <CommentSection
              ticketId={ticket.id}
              canComment={canComment}
              ticketStatus={ticket.status}
            />
          </div>
        </div>

        {/* Status Message for non-approved tickets */}
        {!canComment && (
          <div className="border-t border-gray-200 p-4 bg-yellow-50">
            <div className="flex items-center text-yellow-800">
              <AlertCircle className="w-5 h-5 mr-2" />
              <p className="text-sm">
                Comments can only be added after the ticket has been approved by
                an agent.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TicketDetailModal;
