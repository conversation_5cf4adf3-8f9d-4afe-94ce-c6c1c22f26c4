import {
  MessageSquare,
  MoreVert<PERSON>,
  Paperclip,
  Send,
  Smile,
} from "lucide-react";
import { useEffect, useState } from "react";

const CommentSection = ({ ticketId, canComment, ticketStatus }) => {
  const [comments, setComments] = useState([]);
  const [newMessage, setNewMessage] = useState("");

  // Sample comments data - replace with actual API call
  useEffect(() => {
    const sampleComments = [
      {
        id: 1,
        author: "<PERSON> Adams",
        role: "Customer",
        content:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad",
        timestamp: "3 days ago",
        isCustomer: true,
        attachments: [],
        avatar: "D",
        avatarColor: "bg-teal-500",
      },
      {
        id: 2,
        author: "UI/UX Designer Member",
        role: "Agent",
        content: "UI/UX Designer Needs...",
        timestamp: "3 days ago",
        isCustomer: false,
        attachments: [],
        avatar: "U",
        avatarColor: "bg-teal-500",
      },
      {
        id: 3,
        author: "Agent",
        role: "Agent",
        content:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad",
        timestamp: "3 days ago",
        isCustomer: false,
        attachments: [],
        avatar: "M",
        avatarColor: "bg-gray-500",
      },
    ];
    setComments(sampleComments);
  }, [ticketId]);

  const handleSendMessage = () => {
    if (!newMessage.trim()) return;

    const newComment = {
      id: comments.length + 1,
      author: "You",
      role: "Customer",
      content: newMessage,
      timestamp: "Just now",
      isCustomer: true,
      attachments: [],
      avatar: "Y",
      avatarColor: "bg-blue-500",
    };

    setComments([...comments, newComment]);
    setNewMessage("");
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (comments.length === 0) {
    return (
      <div className="flex flex-col h-96">
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <MessageSquare className="w-12 h-12 mx-auto mb-3 text-gray-300" />
            <p>
              No messages yet.{" "}
              {canComment
                ? "Start the conversation!"
                : "Messages will appear here once the ticket is approved."}
            </p>
          </div>
        </div>
        {canComment && (
          <div className="border-t p-4 bg-white">
            <div className="flex items-end space-x-3">
              <div className="flex-1">
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message here..."
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows="2"
                />
              </div>
              <button
                onClick={handleSendMessage}
                disabled={!newMessage.trim()}
                className="bg-teal-500 text-white p-3 rounded-lg hover:bg-teal-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-96 bg-gray-50 rounded-lg">
      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {comments.map((comment) => (
          <div
            key={comment.id}
            className={`flex ${
              comment.isCustomer ? "justify-end" : "justify-start"
            }`}
          >
            <div
              className={`flex max-w-xs lg:max-w-md ${
                comment.isCustomer ? "flex-row-reverse" : "flex-row"
              } space-x-2`}
            >
              {/* Avatar */}
              <div
                className={`w-8 h-8 rounded-full ${
                  comment.avatarColor
                } flex items-center justify-center text-white text-sm font-medium flex-shrink-0 ${
                  comment.isCustomer ? "ml-2" : "mr-2"
                }`}
              >
                {comment.avatar}
              </div>

              {/* Message Bubble */}
              <div className="flex flex-col">
                {/* Author and timestamp */}
                <div
                  className={`text-xs text-gray-500 mb-1 ${
                    comment.isCustomer ? "text-right" : "text-left"
                  }`}
                >
                  <span className="font-medium">{comment.author}</span>
                </div>

                {/* Message content */}
                <div
                  className={`rounded-lg px-4 py-2 ${
                    comment.isCustomer
                      ? "bg-teal-500 text-white"
                      : "bg-white border border-gray-200 text-gray-900"
                  }`}
                >
                  <p className="text-sm">{comment.content}</p>
                </div>

                {/* Timestamp */}
                <div
                  className={`text-xs text-gray-400 mt-1 ${
                    comment.isCustomer ? "text-right" : "text-left"
                  }`}
                >
                  {comment.timestamp}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Chat Input */}
      {canComment && (
        <div className="border-t p-4 bg-white rounded-b-lg">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Paperclip className="w-5 h-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Smile className="w-5 h-5" />
              </button>
            </div>

            <div className="flex-1 flex items-center space-x-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder="Type your message here..."
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              />
              <button
                onClick={handleSendMessage}
                disabled={!newMessage.trim()}
                className="bg-teal-500 text-white p-2 rounded-lg hover:bg-teal-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                <Send className="w-5 h-5" />
              </button>
            </div>

            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <MoreVertical className="w-5 h-5" />
            </button>
          </div>

          {/* Action buttons */}
          <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <button className="flex items-center space-x-1 hover:text-gray-700 transition-colors">
                <span>Reply</span>
              </button>
              <button className="flex items-center space-x-1 hover:text-gray-700 transition-colors">
                <span>Note</span>
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <button className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 transition-colors">
                <span>Open</span>
              </button>
              <button className="flex items-center space-x-1 text-sm text-gray-500 hover:text-gray-700 transition-colors">
                <span>Send</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommentSection;
